import 'dotenv/config';
import { MonikaBot } from './src/core/MonikaBot.js';
import { AIService } from './src/services/AIService.js';
import { MemoryService } from './src/services/MemoryService.js';
import { StatusService } from './src/services/StatusService.js';
import { MessageService } from './src/services/MessageService.js';
import { botConfig } from './src/config/botConfig.js';
import { DiscordMessage } from './src/types/message.js';

async function main() {
    console.log('🌸 Initializing Monika Bot...');
    console.log('📅 Started at:', new Date().toISOString());

    // Create the bot instance
    const bot = new MonikaBot(botConfig);

    // Create all services
    const aiService = new AIService(botConfig);
    const memoryService = new MemoryService(botConfig);
    const statusService = new StatusService(bot.getClient(), botConfig.status, botConfig);
    const messageService = new MessageService(botConfig, aiService, memoryService, statusService);

    // Register services with the bot
    bot.registerService('ai', aiService);
    bot.registerService('memory', memoryService);
    bot.registerService('status', statusService);
    bot.registerService('message', messageService);

    // Set up event handlers
    bot.setEventHandlers({
        onReady: async () => {
            console.log('🎉 All services initialized and ready!');

            // Test AI service connection
            try {
                const isConnected = await aiService.testConnection();
                if (isConnected) {
                    console.log('✅ AI service connection successful');
                } else {
                    console.log('❌ AI service connection failed');
                }
            } catch (error) {
                console.error('❌ Error testing AI connection:', error);
            }
        },

        onMessage: async (message: DiscordMessage) => {
            await messageService.handleMessage(bot.getClient(), message);
        },

        onTypingStart: async (data: any) => {
            await messageService.handleTypingStart(data);
        },

        onError: (error: Error) => {
            console.error('🚨 Bot error:', error);
        },

        onDisconnect: () => {
            console.log('🔌 Bot disconnected');
        }
    });

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Received SIGINT, shutting down gracefully...');
        try {
            await bot.stop();
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    });

    process.on('SIGTERM', async () => {
        console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
        try {
            await bot.stop();
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        console.error('❌ Uncaught Exception:', error);
        process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
        console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
        process.exit(1);
    });

    try {
        await bot.start();
        console.log('✅ Monika Bot is now running!');
        console.log('💚 Ready to chat with you~');

        // Optional: Log some stats periodically
        setInterval(() => {
            if (bot.isOnline()) {
                const conversations = messageService.getAllConversations();
                const statusStats = statusService.getStats();
                console.log(`📊 Status: ${statusStats.currentStatus} | Active conversations: ${conversations.length}`);
            }
        }, 300000); // Every 5 minutes

    } catch (error) {
        console.error('❌ Failed to start Monika Bot:', error);
        process.exit(1);
    }
}

// Start the bot
main().catch((error) => {
    console.error('❌ Fatal error in main:', error);
    process.exit(1);
});