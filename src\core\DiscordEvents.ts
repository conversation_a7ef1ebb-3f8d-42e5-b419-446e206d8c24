/**
 * Centralized Discord Events Handler
 * This class provides a clean interface for all Discord events
 * and allows easy registration of event handlers
 */

export type EventHandler<T = any> = (data: T) => void | Promise<void>;

export interface DiscordEventHandlers {
  // Official Discord Events
  voice_server_update?: EventHandler;
  user_update?: EventHandler;
  application_command_create?: EventHandler;
  application_command_update?: EventHandler;
  application_command_delete?: EventHandler;
  interaction_create?: EventHandler;
  guild_create?: EventHandler;
  guild_delete?: EventHandler;
  guild_role_create?: EventHandler;
  guild_role_update?: EventHandler;
  guild_role_delete?: EventHandler;
  thread_create?: EventHandler;
  thread_join?: EventHandler;
  thread_update?: EventHandler;
  thread_delete?: EventHandler;
  thread_list_sync?: EventHandler;
  thread_member_update?: EventHandler;
  thread_members_update?: EventHandler;
  channel_create?: EventHandler;
  channel_update?: EventHandler;
  channel_delete?: EventHandler;
  channel_pins_update?: EventHandler;
  guild_member_add?: EventHandler;
  guild_member_update?: EventHandler;
  guild_member_remove?: EventHandler;
  guild_ban_add?: EventHandler;
  guild_ban_remove?: EventHandler;
  guild_emojis_update?: EventHandler;
  guild_stickers_update?: EventHandler;
  guild_integrations_update?: EventHandler;
  guild_webhooks_update?: EventHandler;
  invite_create?: EventHandler;
  invite_delete?: EventHandler;
  voice_state_update?: EventHandler;
  presence_update?: EventHandler;
  message_update?: EventHandler;
  message_delete?: EventHandler;
  message_delete_bulk?: EventHandler;
  message_reaction_add?: EventHandler;
  message_reaction_remove?: EventHandler;
  message_reaction_remove_all?: EventHandler;
  message_reaction_remove_emoji?: EventHandler;
  typing_start?: EventHandler;
  relationship_add?: EventHandler;
  relationship_remove?: EventHandler;

  // Unofficial/Custom Events
  discord_disconnect?: EventHandler;
  discord_reconnect?: EventHandler;
  gateway?: EventHandler;
  heartbeat_sent?: EventHandler;
  heartbeat_received?: EventHandler;
  ready?: EventHandler;
  message?: EventHandler;
  default?: EventHandler;
  recipient_add?: EventHandler;
  recipient_remove?: EventHandler;
  call?: EventHandler;
  channel_name_change?: EventHandler;
  channel_icon_change?: EventHandler;
  channel_pinned_message?: EventHandler;
  user_join?: EventHandler;
  guild_boost?: EventHandler;
  guild_boost_tier_1?: EventHandler;
  guild_boost_tier_2?: EventHandler;
  guild_boost_tier_3?: EventHandler;
  channel_follow_add?: EventHandler;
  guild_discovery_disqualified?: EventHandler;
  guild_discovery_requalified?: EventHandler;
  guild_discovery_grace_period_initial_warning?: EventHandler;
  guild_discovery_grace_period_final_warning?: EventHandler;
  thread_created?: EventHandler;
  reply?: EventHandler;
  chat_input_command?: EventHandler;
  thread_starter_message?: EventHandler;
  guild_invite_reminder?: EventHandler;
  context_menu_command?: EventHandler;
  auto_moderation_action?: EventHandler;
  role_subscription_purchase?: EventHandler;
  interaction_premium_upsell?: EventHandler;
  stage_start?: EventHandler;
  stage_end?: EventHandler;
  stage_speaker?: EventHandler;
  stage_topic?: EventHandler;
  guild_application_premium_subscription?: EventHandler;

  // Bot lifecycle events
  error?: EventHandler<Error>;
  disconnect?: EventHandler;
}

export class DiscordEvents {
  private handlers: DiscordEventHandlers = {};
  private client: any;

  constructor(client: any) {
    this.client = client;
  }

  /**
   * Register an event handler
   */
  on<K extends keyof DiscordEventHandlers>(event: K, handler: DiscordEventHandlers[K]): void {
    this.handlers[event] = handler;
  }

  /**
   * Remove an event handler
   */
  off<K extends keyof DiscordEventHandlers>(event: K): void {
    delete this.handlers[event];
  }

  /**
   * Initialize all event listeners on the Discord client
   */
  initialize(): void {
    console.log('🔗 Initializing Discord event listeners...');

    // Register all possible Discord events
    this.registerOfficialEvents();
    this.registerUnofficialEvents();
    this.registerLifecycleEvents();

    console.log('✅ Discord event listeners initialized');
  }

  /**
   * Register official Discord API events
   */
  private registerOfficialEvents(): void {
    const officialEvents = [
      'voice_server_update', 'user_update', 'application_command_create',
      'application_command_update', 'application_command_delete', 'interaction_create',
      'guild_create', 'guild_delete', 'guild_role_create', 'guild_role_update',
      'guild_role_delete', 'thread_create', 'thread_join', 'thread_update',
      'thread_delete', 'thread_list_sync', 'thread_member_update', 'thread_members_update',
      'channel_create', 'channel_update', 'channel_delete', 'channel_pins_update',
      'guild_member_add', 'guild_member_update', 'guild_member_remove',
      'guild_ban_add', 'guild_ban_remove', 'guild_emojis_update', 'guild_stickers_update',
      'guild_integrations_update', 'guild_webhooks_update', 'invite_create',
      'invite_delete', 'voice_state_update', 'presence_update', 'message_update',
      'message_delete', 'message_delete_bulk', 'message_reaction_add',
      'message_reaction_remove', 'message_reaction_remove_all', 'message_reaction_remove_emoji',
      'typing_start', 'relationship_add', 'relationship_remove'
    ];

    officialEvents.forEach(event => {
      this.client.on(event, (data: any) => this.handleEvent(event as keyof DiscordEventHandlers, data));
    });
  }

  /**
   * Register unofficial/custom events
   */
  private registerUnofficialEvents(): void {
    const unofficialEvents = [
      'discord_disconnect', 'discord_reconnect', 'gateway', 'heartbeat_sent',
      'heartbeat_received', 'ready', 'message', 'default', 'recipient_add',
      'recipient_remove', 'call', 'channel_name_change', 'channel_icon_change',
      'channel_pinned_message', 'user_join', 'guild_boost', 'guild_boost_tier_1',
      'guild_boost_tier_2', 'guild_boost_tier_3', 'channel_follow_add',
      'guild_discovery_disqualified', 'guild_discovery_requalified',
      'guild_discovery_grace_period_initial_warning', 'guild_discovery_grace_period_final_warning',
      'thread_created', 'reply', 'chat_input_command', 'thread_starter_message',
      'guild_invite_reminder', 'context_menu_command', 'auto_moderation_action',
      'role_subscription_purchase', 'interaction_premium_upsell', 'stage_start',
      'stage_end', 'stage_speaker', 'stage_topic', 'guild_application_premium_subscription'
    ];

    unofficialEvents.forEach(event => {
      this.client.on(event, (data: any) => this.handleEvent(event as keyof DiscordEventHandlers, data));
    });
  }

  /**
   * Register bot lifecycle events
   */
  private registerLifecycleEvents(): void {
    this.client.on('error', (error: Error) => this.handleEvent('error', error));
    this.client.on('disconnect', () => this.handleEvent('disconnect', undefined));
  }

  /**
   * Handle an event by calling the registered handler
   */
  private async handleEvent<K extends keyof DiscordEventHandlers>(
    event: K, 
    data: any
  ): Promise<void> {
    try {
      const handler = this.handlers[event];
      if (handler) {
        await handler(data);
      }
    } catch (error) {
      console.error(`❌ Error handling event '${String(event)}':`, error);
    }
  }

  /**
   * Get the current client instance
   */
  getClient(): any {
    return this.client;
  }

  /**
   * Check if a handler is registered for an event
   */
  hasHandler<K extends keyof DiscordEventHandlers>(event: K): boolean {
    return !!this.handlers[event];
  }

  /**
   * Get all registered event names
   */
  getRegisteredEvents(): string[] {
    return Object.keys(this.handlers);
  }
}
