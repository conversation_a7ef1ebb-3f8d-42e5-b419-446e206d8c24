/**
 * Simplified Monika Bot Core
 * This class focuses only on initialization and coordination
 * All business logic is handled by dedicated services
 */

import * as Discord from 'discord-user-bots';
import { DiscordEvents } from './DiscordEvents.js';
import { IBotConfig, IEventHandlers } from './interfaces.js';
import { DiscordMessage } from '../types/message.js';

export class MonikaBot {
  private client: any;
  private events: DiscordEvents;
  private config: IBotConfig;
  private isReady: boolean = false;
  private services: Map<string, any> = new Map();
  private eventHandlers: IEventHandlers = {};

  constructor(config: IBotConfig) {
    this.config = config;
    this.client = new Discord.Client();
    this.events = new DiscordEvents(this.client);
    this.setupCoreEventHandlers();
  }

  /**
   * Register a service with the bot
   */
  registerService<T>(name: string, service: T): void {
    this.services.set(name, service);
    console.log(`📦 Registered service: ${name}`);
  }

  /**
   * Get a registered service
   */
  getService<T>(name: string): T | undefined {
    return this.services.get(name);
  }

  /**
   * Register event handlers
   */
  setEventHandlers(handlers: IEventHandlers): void {
    this.eventHandlers = handlers;
  }

  /**
   * Setup core event handlers that the bot always needs
   */
  private setupCoreEventHandlers(): void {
    // Ready event
    this.events.on('ready', async () => {
      await this.onReady();
    });

    // Message event
    this.events.on('message', async (message: any) => {
      await this.onMessage(message);
    });

    // Typing start event
    this.events.on('typing_start', async (data: any) => {
      await this.onTypingStart(data);
    });

    // Error event
    this.events.on('error', (error: Error) => {
      this.onError(error);
    });

    // Disconnect event
    this.events.on('disconnect', () => {
      this.onDisconnect();
    });
  }

  /**
   * Handle ready event
   */
  private async onReady(): Promise<void> {
    console.log('🌟 Monika is online and ready!');
    console.log(`📝 Personality: ${this.config.personality.traits.join(', ')}`);
    console.log(`🎯 Target User ID: ${this.config.targetUserId || 'All users'}`);
    console.log(`💬 Max Context Messages: ${this.config.maxContextMessages}`);
    console.log(`📚 Max Stored Messages: ${this.config.maxStoredMessages}`);

    this.isReady = true;

    // Initialize all registered services
    await this.initializeServices();

    // Call custom ready handler if provided
    if (this.eventHandlers.onReady) {
      await this.eventHandlers.onReady();
    }
  }

  /**
   * Handle message event
   */
  private async onMessage(message: any): Promise<void> {
    try {
      // Skip if bot is not ready
      if (!this.isReady) {
        return;
      }

      // Convert to our message type
      const discordMessage: DiscordMessage = this.convertToDiscordMessage(message);

      // Skip bot's own messages
      if (discordMessage.author.id === this.config.botId) {
        return;
      }

      // Log the message for debugging
      console.log(`📨 Message received:`, {
        author: discordMessage.author.username || discordMessage.author.global_name,
        content: discordMessage.content.substring(0, 100) + (discordMessage.content.length > 100 ? '...' : ''),
        channel: discordMessage.channel_id
      });

      // Call custom message handler if provided
      if (this.eventHandlers.onMessage) {
        await this.eventHandlers.onMessage(discordMessage);
      }

    } catch (error) {
      console.error('❌ Error processing message:', error);
    }
  }

  /**
   * Handle typing start event
   */
  private async onTypingStart(data: any): Promise<void> {
    try {
      if (this.eventHandlers.onTypingStart) {
        await this.eventHandlers.onTypingStart(data);
      }
    } catch (error) {
      console.error('❌ Error handling typing start:', error);
    }
  }

  /**
   * Handle error event
   */
  private onError(error: Error): void {
    console.error('❌ Discord client error:', error);
    if (this.eventHandlers.onError) {
      this.eventHandlers.onError(error);
    }
  }

  /**
   * Handle disconnect event
   */
  private onDisconnect(): void {
    console.log('🔌 Monika disconnected from Discord');
    this.isReady = false;
    if (this.eventHandlers.onDisconnect) {
      this.eventHandlers.onDisconnect();
    }
  }

  /**
   * Convert raw Discord message to our DiscordMessage type
   */
  private convertToDiscordMessage(message: any): DiscordMessage {
    return {
      id: message.id || Date.now().toString(),
      content: message.content || '',
      author: {
        id: message.author?.id || 'unknown',
        username: message.author?.username || 'Unknown User',
        discriminator: message.author?.discriminator,
        global_name: message.author?.global_name
      },
      channel_id: message.channel_id || message.channelId || '',
      timestamp: message.timestamp || new Date().toISOString(),
      edited_timestamp: message.edited_timestamp,
      attachments: message.attachments,
      embeds: message.embeds,
      mentions: message.mentions,
      mention_roles: message.mention_roles,
      pinned: message.pinned,
      mention_everyone: message.mention_everyone,
      tts: message.tts,
      type: message.type
    };
  }

  /**
   * Initialize all registered services
   */
  private async initializeServices(): Promise<void> {
    console.log('🔧 Initializing services...');
    
    for (const [name, service] of this.services) {
      try {
        if (service.initialize && typeof service.initialize === 'function') {
          await service.initialize();
          console.log(`✅ Service initialized: ${name}`);
        }
      } catch (error) {
        console.error(`❌ Failed to initialize service ${name}:`, error);
      }
    }
  }

  /**
   * Start the bot
   */
  async start(): Promise<void> {
    try {
      // Check if required environment variables are set
      if (!process.env.USER_TOKEN) {
        throw new Error('USER_TOKEN environment variable is required');
      }

      if (!process.env.DEEPSEEK_API_KEY) {
        throw new Error('DEEPSEEK_API_KEY environment variable is required');
      }

      console.log('🚀 Starting Monika bot...');
      console.log('🔑 Logging in to Discord...');
      
      // Initialize event system
      this.events.initialize();
      
      // Login to Discord
      await this.client.login(process.env.USER_TOKEN);
      
    } catch (error) {
      console.error('❌ Failed to start bot:', error);
      throw error;
    }
  }

  /**
   * Stop the bot
   */
  async stop(): Promise<void> {
    try {
      console.log('🛑 Stopping Monika bot...');
      this.isReady = false;

      // Stop all services
      await this.stopServices();

      // Destroy Discord client
      if (this.client) {
        await this.client.destroy();
      }

      console.log('👋 Monika has been stopped');
    } catch (error) {
      console.error('❌ Error stopping bot:', error);
      throw error;
    }
  }

  /**
   * Stop all registered services
   */
  private async stopServices(): Promise<void> {
    for (const [name, service] of this.services) {
      try {
        if (service.stop && typeof service.stop === 'function') {
          await service.stop();
          console.log(`🛑 Service stopped: ${name}`);
        }
      } catch (error) {
        console.error(`❌ Failed to stop service ${name}:`, error);
      }
    }
  }

  /**
   * Get the Discord client
   */
  getClient(): any {
    return this.client;
  }

  /**
   * Get the events handler
   */
  getEvents(): DiscordEvents {
    return this.events;
  }

  /**
   * Check if bot is online
   */
  isOnline(): boolean {
    return this.isReady;
  }

  /**
   * Get bot configuration
   */
  getConfig(): IBotConfig {
    return this.config;
  }

  /**
   * Update bot configuration
   */
  updateConfig(newConfig: Partial<IBotConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Bot configuration updated');
  }

  /**
   * Send a message programmatically
   */
  async sendMessage(channelId: string, content: string): Promise<void> {
    try {
      if (!this.isReady) {
        throw new Error('Bot is not ready');
      }

      await this.client.send(channelId, { content });
      console.log(`📤 Sent message to ${channelId}: ${content.substring(0, 50)}...`);
    } catch (error) {
      console.error('❌ Error sending message:', error);
      throw error;
    }
  }
}
