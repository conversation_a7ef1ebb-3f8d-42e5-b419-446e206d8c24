/**
 * Core interfaces for the Discord bot services
 */

import { DiscordMessage } from '../types/message.js';

export interface IBotService {
  initialize(): Promise<void>;
  stop(): Promise<void>;
  isReady(): boolean;
}

export interface IAIService extends IBotService {
  generateResponse(messages: any[], newMessage: string, memoryContext?: any): Promise<string>;
  generateContextualResponse(conversationHistory: any[], newMessage: string, memoryContext?: any): Promise<string>;
  testConnection(): Promise<boolean>;
  customAiRequest(systemPrompt: string, userPrompt: string): Promise<string>;
}

export interface IMemoryService extends IBotService {
  processMessageForMemory(message: DiscordMessage): Promise<boolean>;
  processMessagesWithContext(messages: DiscordMessage[], userId: string): Promise<boolean>;
  getMemoryContext(userId: string, currentMessage?: string): Promise<any>;
  getMemoryStats(userId: string): Promise<any>;
  addManualMemory(userId: string, memoryText: string, category?: string): Promise<boolean>;
  clearUserMemories(userId: string): Promise<boolean>;
}

export interface IStatusService extends IBotService {
  setStatus(status: string): Promise<void>;
  getCurrentStatus(): string;
  onMessageActivity(): Promise<void>;
  getStats(): any;
  updateConfig(config: any): void;
}

export interface IMessageService extends IBotService {
  handleMessage(client: any, message: DiscordMessage): Promise<void>;
  shouldRespondToMessage(message: DiscordMessage): boolean;
  getConversationStats(channelId: string): any;
  exportConversation(channelId: string): Promise<string | null>;
  clearConversation(channelId: string): Promise<boolean>;
  getAllConversations(): string[];
}

export interface IBotConfig {
  botId: string;
  targetUserId?: string;
  maxContextMessages: number;
  maxStoredMessages: number;
  maxResponseLength: number;
  personality: {
    name: string;
    traits: string[];
    responseStyle: string;
  };
  status: any;
  responseDelay: {
    enabled: boolean;
    minDelay: number;
    maxDelay: number;
  };
  memory: {
    enabled: boolean;
    maxMemoriesPerUser: number;
    memoryCategories: string[];
  };
}

export interface IEventHandlers {
  onReady?: () => Promise<void>;
  onMessage?: (message: DiscordMessage) => Promise<void>;
  onTypingStart?: (data: any) => Promise<void>;
  onError?: (error: Error) => void;
  onDisconnect?: () => void;
}
