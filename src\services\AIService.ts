/**
 * Refactored AI Service
 * Clean implementation of AI functionality with proper interface compliance
 */

import OpenAI from 'openai';
import { IAIService, IBotConfig } from '../core/interfaces.js';
import { ChatMessage, MemoryContext } from '../types/message.js';

export class AIService implements IAIService {
  private openai: OpenAI;
  private config: IBotConfig;
  private ready: boolean = false;

  constructor(config: IBotConfig) {
    this.config = config;
    this.openai = new OpenAI({
      baseURL: 'https://api.deepseek.com',
      apiKey: process.env.DEEPSEEK_API_KEY,
    });
  }

  async initialize(): Promise<void> {
    console.log('🤖 Initializing AI Service...');
    
    // Test connection
    const isConnected = await this.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to AI service');
    }
    
    this.ready = true;
    console.log('✅ AI Service initialized successfully');
  }

  async stop(): Promise<void> {
    console.log('🛑 Stopping AI Service...');
    this.ready = false;
  }

  isReady(): boolean {
    return this.ready;
  }

  async testConnection(): Promise<boolean> {
    try {
      const testCompletion = await this.openai.chat.completions.create({
        messages: [{ role: 'user', content: 'Hello' }],
        model: 'deepseek-chat',
        max_tokens: 10,
      });
      
      return !!testCompletion.choices[0]?.message?.content;
    } catch (error) {
      console.error('AI service connection test failed:', error);
      return false;
    }
  }

  async customAiRequest(systemPrompt: string, userPrompt: string): Promise<string> {
    if (!this.ready) {
      throw new Error('AI Service not ready');
    }

    try {
      const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ];

      const completion = await this.openai.chat.completions.create({
        messages: conversationMessages,
        model: 'deepseek-chat',
        max_tokens: 500,
        temperature: 0.8,
        presence_penalty: 0.1,
        frequency_penalty: 0.1,
      });

      const response = completion.choices[0]?.message?.content || 'Sorry, I couldn\'t generate a response.';
      return response.length > this.config.maxResponseLength ? 
        response.substring(0, this.config.maxResponseLength - 3) + '...' : 
        response;

    } catch (error) {
      console.error('Error in custom AI request:', error);
      throw error;
    }
  }

  async generateResponse(messages: any[], newMessage: string, memoryContext?: any): Promise<string> {
    if (!this.ready) {
      throw new Error('AI Service not ready');
    }

    try {
      const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: this.buildSystemPrompt(memoryContext)
        }
      ];

      // Add conversation history
      messages.forEach(msg => {
        conversationMessages.push({
          role: msg.role,
          content: msg.content
        });
      });

      // Add the new message
      conversationMessages.push({
        role: 'user',
        content: newMessage
      });

      const completion = await this.openai.chat.completions.create({
        messages: conversationMessages,
        model: 'deepseek-chat',
        max_tokens: 1000,
        temperature: 0.9,
        presence_penalty: 0.2,
        frequency_penalty: 0.1,
      });

      const response = completion.choices[0]?.message?.content || 'Sorry, I couldn\'t generate a response.';
      return response.length > this.config.maxResponseLength ? 
        response.substring(0, this.config.maxResponseLength - 3) + '...' : 
        response;

    } catch (error) {
      console.error('Error generating response:', error);
      return 'Sorry, I encountered an error while generating a response.';
    }
  }

  async generateContextualResponse(
    conversationHistory: any[], 
    newMessage: string, 
    context?: any,
    memoryContext?: any
  ): Promise<string> {
    if (!this.ready) {
      throw new Error('AI Service not ready');
    }

    try {
      const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: this.buildContextualSystemPrompt(context, memoryContext)
        }
      ];

      // Add conversation history (limit to prevent token overflow)
      const recentHistory = conversationHistory.slice(-20);
      recentHistory.forEach(msg => {
        conversationMessages.push({
          role: msg.role,
          content: msg.content
        });
      });

      // Add the new message
      conversationMessages.push({
        role: 'user',
        content: newMessage
      });

      const completion = await this.openai.chat.completions.create({
        messages: conversationMessages,
        model: 'deepseek-chat',
        max_tokens: 1000,
        temperature: 0.9,
        presence_penalty: 0.2,
        frequency_penalty: 0.1,
      });

      const response = completion.choices[0]?.message?.content || 'Sorry, I couldn\'t generate a response.';
      return response.length > this.config.maxResponseLength ? 
        response.substring(0, this.config.maxResponseLength - 3) + '...' : 
        response;

    } catch (error) {
      console.error('Error generating contextual response:', error);
      return await this.generateResponse(conversationHistory, newMessage, memoryContext);
    }
  }

  private buildSystemPrompt(memoryContext?: any): string {
    let prompt = `You are ${this.config.personality.name}, a ${this.config.personality.responseStyle} AI assistant. `;
    prompt += `Your personality traits: ${this.config.personality.traits.join(', ')}. `;
    prompt += `Keep responses natural and conversational. `;

    if (memoryContext && memoryContext.recentMemories?.length > 0) {
      prompt += `\n\nWhat you remember about this user:\n`;
      memoryContext.recentMemories.forEach((memory: any) => {
        prompt += `- ${memory.memory}\n`;
      });
    }

    return prompt;
  }

  private buildContextualSystemPrompt(context?: any, memoryContext?: any): string {
    let prompt = this.buildSystemPrompt(memoryContext);

    if (context) {
      if (context.isDirectMessage) {
        prompt += `\nThis is a direct message conversation. Be more personal and intimate in your responses.`;
      } else if (context.channelName) {
        prompt += `\nThis conversation is happening in the "${context.channelName}" channel.`;
      }
    }

    return prompt;
  }
}
