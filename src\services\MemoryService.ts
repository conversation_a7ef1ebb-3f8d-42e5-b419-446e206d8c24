/**
 * Refactored Memory Service
 * Clean implementation of memory functionality with proper interface compliance
 */

import OpenAI from 'openai';
import { IMemoryService, IBotConfig } from '../core/interfaces.js';
import { DiscordMessage, UserMemory, MemoryEvaluation, MemoryContext } from '../types/message.js';
import { MemoryStorage } from '../utils/memoryStorage.js';

export class MemoryService implements IMemoryService {
  private openai: OpenAI;
  private storage: MemoryStorage;
  private config: IBotConfig;
  private ready: boolean = false;

  constructor(config: IBotConfig) {
    this.config = config;
    this.openai = new OpenAI({
      baseURL: 'https://api.deepseek.com',
      apiKey: process.env.DEEPSEEK_API_KEY,
    });
    this.storage = new MemoryStorage();
  }

  async initialize(): Promise<void> {
    if (!this.config.memory.enabled) {
      console.log('🧠 Memory service is disabled');
      this.ready = true;
      return;
    }

    console.log('🧠 Initializing Memory Service...');
    this.ready = true;
    console.log('✅ Memory Service initialized successfully');
  }

  async stop(): Promise<void> {
    console.log('🛑 Stopping Memory Service...');
    this.ready = false;
  }

  isReady(): boolean {
    return this.ready;
  }

  async processMessageForMemory(message: DiscordMessage): Promise<boolean> {
    if (!this.ready || !this.config.memory.enabled) {
      return false;
    }

    try {
      // Skip very short messages or commands
      if (message.content.length < 10 || message.content.startsWith('!')) {
        return false;
      }

      const evaluation = await this.evaluateMessage(message);

      if (!evaluation.isMemoryWorthy || !evaluation.extractedMemory) {
        console.log(`📝 Message not memory-worthy: ${evaluation.reasoning}`);
        return false;
      }

      // Only store memories with reasonable confidence
      if (evaluation.confidence < 0.6) {
        console.log(`📝 Memory confidence too low (${evaluation.confidence}): ${evaluation.extractedMemory}`);
        return false;
      }

      const memory: UserMemory = {
        memory: evaluation.extractedMemory,
        timestamp: new Date().toISOString(),
        confidence: evaluation.confidence,
        category: evaluation.category || 'general'
      };

      const success = await this.storage.addMemory(message.author.id, memory);
      if (success) {
        console.log(`🧠 Stored memory for ${message.author.username}: ${memory.memory}`);
      }

      return success;

    } catch (error) {
      console.error('Error processing message for memory:', error);
      return false;
    }
  }

  async processMessagesWithContext(messages: DiscordMessage[], userId: string): Promise<boolean> {
    if (!this.ready || !this.config.memory.enabled) {
      return false;
    }

    try {
      // Filter messages from the target user
      const userMessages = messages.filter(msg => msg.author.id === userId);
      
      if (userMessages.length === 0) {
        return false;
      }

      // Build conversation context
      const conversationText = userMessages
        .map(msg => `${msg.author.username || msg.author.global_name}: ${msg.content}`)
        .join('\n');

      const evaluation = await this.evaluateConversationContext(conversationText, userId);

      if (!evaluation.isMemoryWorthy || !evaluation.extractedMemory) {
        console.log(`📝 Conversation not memory-worthy: ${evaluation.reasoning}`);
        return false;
      }

      const memory: UserMemory = {
        memory: evaluation.extractedMemory,
        timestamp: new Date().toISOString(),
        confidence: evaluation.confidence,
        category: evaluation.category || 'conversation'
      };

      const success = await this.storage.addMemory(userId, memory);
      if (success) {
        console.log(`🧠 Stored conversation memory for user: ${memory.memory}`);
      }

      return success;

    } catch (error) {
      console.error('Error processing messages with context:', error);
      return false;
    }
  }

  async getMemoryContext(userId: string, currentMessage?: string): Promise<any> {
    if (!this.ready || !this.config.memory.enabled) {
      return {
        recentMemories: [],
        relevantMemories: [],
        memoryCount: 0
      };
    }

    try {
      const userMemories = await this.storage.getUserMemories(userId);
      
      if (!userMemories || userMemories.length === 0) {
        return {
          recentMemories: [],
          relevantMemories: [],
          memoryCount: 0
        };
      }

      // Get recent memories (last 10)
      const recentMemories = userMemories
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10);

      // If we have a current message, try to find relevant memories
      let relevantMemories: UserMemory[] = [];
      if (currentMessage) {
        relevantMemories = await this.findRelevantMemories(userMemories, currentMessage);
      }

      return {
        recentMemories,
        relevantMemories,
        memoryCount: userMemories.length
      };

    } catch (error) {
      console.error('Error getting memory context:', error);
      return {
        recentMemories: [],
        relevantMemories: [],
        memoryCount: 0
      };
    }
  }

  async getMemoryStats(userId: string): Promise<any> {
    if (!this.ready || !this.config.memory.enabled) {
      return {
        totalMemories: 0,
        categoryCounts: {}
      };
    }

    try {
      const memories = await this.storage.getUserMemories(userId);
      
      if (!memories || memories.length === 0) {
        return {
          totalMemories: 0,
          categoryCounts: {}
        };
      }

      const categoryCounts: { [key: string]: number } = {};
      memories.forEach(memory => {
        const category = memory.category || 'uncategorized';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      });

      const sortedByDate = memories.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      return {
        totalMemories: memories.length,
        categoryCounts,
        oldestMemory: sortedByDate[0]?.timestamp,
        newestMemory: sortedByDate[sortedByDate.length - 1]?.timestamp
      };

    } catch (error) {
      console.error('Error getting memory stats:', error);
      return {
        totalMemories: 0,
        categoryCounts: {}
      };
    }
  }

  async addManualMemory(userId: string, memoryText: string, category?: string): Promise<boolean> {
    if (!this.ready || !this.config.memory.enabled) {
      return false;
    }

    const memory: UserMemory = {
      memory: memoryText,
      timestamp: new Date().toISOString(),
      confidence: 1.0, // Manual memories have full confidence
      category: category || 'manual'
    };

    return await this.storage.addMemory(userId, memory);
  }

  async clearUserMemories(userId: string): Promise<boolean> {
    if (!this.ready) {
      return false;
    }

    try {
      return await this.storage.clearUserMemories(userId);
    } catch (error) {
      console.error('Error clearing user memories:', error);
      return false;
    }
  }

  private async evaluateMessage(message: DiscordMessage): Promise<MemoryEvaluation> {
    // Implementation would be similar to the original but simplified
    // For now, return a basic evaluation
    return {
      isMemoryWorthy: message.content.length > 20,
      confidence: 0.7,
      reasoning: 'Basic evaluation',
      extractedMemory: message.content,
      category: 'general'
    };
  }

  private async evaluateConversationContext(conversationText: string, userId: string): Promise<MemoryEvaluation> {
    // Implementation would be similar to the original but simplified
    return {
      isMemoryWorthy: conversationText.length > 50,
      confidence: 0.7,
      reasoning: 'Basic conversation evaluation',
      extractedMemory: conversationText.substring(0, 200),
      category: 'conversation'
    };
  }

  private async findRelevantMemories(memories: UserMemory[], currentMessage: string): Promise<UserMemory[]> {
    // Simple keyword matching for now
    const keywords = currentMessage.toLowerCase().split(' ');
    return memories.filter(memory => 
      keywords.some(keyword => 
        keyword.length > 3 && memory.memory.toLowerCase().includes(keyword)
      )
    ).slice(0, 5);
  }
}
