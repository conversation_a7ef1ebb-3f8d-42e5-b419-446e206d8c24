/**
 * Refactored Message Service
 * Clean implementation of message handling with proper interface compliance
 */

import { IMessageService, IBotConfig, IAIService, IMemoryService, IStatusService } from '../core/interfaces.js';
import { DiscordMessage } from '../types/message.js';
import { MessageContextManager } from '../utils/messageContext.js';
import { ConversationStorage } from '../utils/conversationStorage.js';

export class MessageService implements IMessageService {
  private config: IBotConfig;
  private contextManager: MessageContextManager;
  private storage: ConversationStorage;
  private aiService: IAIService;
  private memoryService: IMemoryService;
  private statusService: IStatusService;
  private processingMessages: Set<string> = new Set();
  private ready: boolean = false;

  // Typing management
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private waitingForResponse: Map<string, boolean> = new Map();

  constructor(
    config: IBotConfig,
    aiService: IAIService,
    memoryService: IMemoryService,
    statusService: IStatusService
  ) {
    this.config = config;
    this.aiService = aiService;
    this.memoryService = memoryService;
    this.statusService = statusService;
    this.contextManager = new MessageContextManager(config);
    this.storage = new ConversationStorage();
  }

  async initialize(): Promise<void> {
    console.log('💬 Initializing Message Service...');
    this.ready = true;
    console.log('✅ Message Service initialized successfully');
  }

  async stop(): Promise<void> {
    console.log('🛑 Stopping Message Service...');
    
    // Clear all typing timeouts
    this.typingTimeouts.forEach(timeout => clearTimeout(timeout));
    this.typingTimeouts.clear();
    this.waitingForResponse.clear();
    
    this.ready = false;
  }

  isReady(): boolean {
    return this.ready;
  }

  async handleMessage(client: any, message: DiscordMessage): Promise<void> {
    if (!this.ready) {
      return;
    }

    try {
      // Prevent duplicate processing
      if (this.processingMessages.has(message.id)) {
        return;
      }
      this.processingMessages.add(message.id);

      console.log(`Processing message from ${message.author.username || message.author.id}: ${message.content}`);

      // Always store the message for context
      this.contextManager.addMessageToContext(message.channel_id, message);

      // Check if we should respond to this message
      if (!this.shouldRespondToMessage(message)) {
        return;
      }

      // Handle typing indicators and response delays
      await this.handleTypingAndDelay(client, message);

      // Process message for memory (async, don't wait)
      this.processMessageForMemory(message).catch(error => {
        console.error('Error processing message for memory:', error);
      });

      // Generate and send response
      await this.generateAndSendResponse(client, message);

      // Update status service with activity
      await this.statusService.onMessageActivity();

    } catch (error) {
      console.error('❌ Error handling message:', error);
    } finally {
      this.processingMessages.delete(message.id);
    }
  }

  shouldRespondToMessage(message: DiscordMessage): boolean {
    // Skip bot's own messages
    if (message.author.id === this.config.botId) {
      return false;
    }

    // If target user is specified, only respond to them
    if (this.config.targetUserId && message.author.id !== this.config.targetUserId) {
      console.log(`🔕 Ignored message from ${message.author.username || message.author.global_name}`);
      return false;
    }

    // Skip empty messages
    if (!message.content || message.content.trim().length === 0) {
      return false;
    }

    return true;
  }

  private async handleTypingAndDelay(client: any, message: DiscordMessage): Promise<void> {
    const channelId = message.channel_id;

    // Cancel any existing typing timeout for this channel
    const existingTimeout = this.typingTimeouts.get(channelId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set waiting flag
    this.waitingForResponse.set(channelId, true);

    // Wait for the configured delay
    if (this.config.responseDelay.enabled) {
      const delay = Math.random() * 
        (this.config.responseDelay.maxDelay - this.config.responseDelay.minDelay) + 
        this.config.responseDelay.minDelay;
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Check if we're still supposed to respond (user might have sent another message)
    if (!this.waitingForResponse.get(channelId)) {
      return;
    }

    // Start typing indicator
    try {
      if (client.typing) {
        await client.typing(channelId);
      } else if (client.startTyping) {
        await client.startTyping(channelId);
      }
    } catch (error) {
      console.error('Error starting typing indicator:', error);
    }
  }

  private async generateAndSendResponse(client: any, message: DiscordMessage): Promise<void> {
    try {
      // Get conversation context
      const context = this.contextManager.getContext(message.channel_id);
      const chatMessages = this.contextManager.convertToChatMessages(context, this.config.botId);

      // Get memory context for the user
      const memoryContext = await this.memoryService.getMemoryContext(
        message.author.id,
        message.content
      );

      // Generate AI response
      console.log('Generating AI response...');
      const response = await this.aiService.generateContextualResponse(
        chatMessages,
        message.content,
        {
          isDirectMessage: this.isDirectMessage(message),
          channelName: await this.getChannelName(client, message.channel_id),
        },
        memoryContext
      );

      // Send the response
      await client.send(message.channel_id, { content: response });
      console.log(`📤 Sent response: ${response.substring(0, 100)}...`);

      // Store bot's response in context
      const botMessage: DiscordMessage = {
        id: Date.now().toString(),
        content: response,
        author: {
          id: this.config.botId,
          username: this.config.personality.name,
        },
        channel_id: message.channel_id,
        timestamp: new Date().toISOString(),
      };
      this.contextManager.addMessageToContext(message.channel_id, botMessage);

    } catch (error) {
      console.error('Error generating/sending response:', error);
      
      // Send error message
      try {
        await client.send(message.channel_id, { 
          content: 'Sorry, I encountered an error while processing your message.' 
        });
      } catch (sendError) {
        console.error('Error sending error message:', sendError);
      }
    } finally {
      // Clear waiting flag
      this.waitingForResponse.set(message.channel_id, false);
    }
  }

  private async processMessageForMemory(message: DiscordMessage): Promise<void> {
    try {
      // Skip very short messages or commands
      if (message.content.length < 10 || message.content.startsWith('!')) {
        return;
      }

      console.log(`🧠 Evaluating message for memory: ${message.content.substring(0, 50)}...`);
      await this.memoryService.processMessageForMemory(message);

    } catch (error) {
      console.error('Error in processMessageForMemory:', error);
    }
  }

  private isDirectMessage(message: DiscordMessage): boolean {
    // Simple check - in a real implementation, you'd check the channel type
    return message.channel_id.length < 20; // DM channel IDs are typically shorter
  }

  private async getChannelName(client: any, channelId: string): Promise<string> {
    try {
      // In a real implementation, you'd fetch channel info from Discord
      return 'general';
    } catch (error) {
      return 'unknown';
    }
  }

  // Public interface methods
  getConversationStats(channelId: string): any {
    const context = this.contextManager.getContext(channelId);
    return {
      messageCount: context.length,
      lastMessage: context[context.length - 1]?.timestamp,
      participants: [...new Set(context.map(msg => msg.author.id))]
    };
  }

  async exportConversation(channelId: string): Promise<string | null> {
    try {
      return this.storage.exportConversationAsText(channelId);
    } catch (error) {
      console.error('Error exporting conversation:', error);
      return null;
    }
  }

  async clearConversation(channelId: string): Promise<boolean> {
    try {
      this.contextManager.clearContext(channelId);
      return this.storage.deleteConversation(channelId);
    } catch (error) {
      console.error('Error clearing conversation:', error);
      return false;
    }
  }

  getAllConversations(): string[] {
    return this.contextManager.getAllChannels();
  }

  // Typing event handler
  async handleTypingStart(data: any): Promise<void> {
    if (!this.ready) {
      return;
    }

    const channelId = data.channel_id;
    const userId = data.user_id;

    // If this is from the target user and we're waiting to respond, pause our response
    if (this.config.targetUserId === userId && this.waitingForResponse.get(channelId)) {
      console.log('🔄 User is typing, pausing response...');
      
      // Set a timeout to check if user is still typing
      const timeout = setTimeout(() => {
        console.log('⏰ Typing timeout, resuming response...');
        this.waitingForResponse.set(channelId, true);
      }, 1000); // Wait 1 second

      this.typingTimeouts.set(channelId, timeout);
      this.waitingForResponse.set(channelId, false);
    }
  }
}
