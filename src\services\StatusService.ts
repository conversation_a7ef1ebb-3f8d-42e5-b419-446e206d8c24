/**
 * Refactored Status Service
 * Clean implementation of status management with proper interface compliance
 */

import { IStatusService, IBotConfig } from '../core/interfaces.js';

export type DiscordStatus = 'online' | 'idle' | 'dnd' | 'invisible';

export interface StatusConfig {
  enabled: boolean;
  statuses: {
    [key: string]: DiscordStatus;
  };
  autoManagement: {
    enabled: boolean;
    defaultStatus: DiscordStatus;
    goIdleAfterInactivity: boolean;
  };
  idleTimeout: number; // minutes
}

export interface StatusManagerEvents {
  statusChanged?: (oldStatus: DiscordStatus, newStatus: DiscordStatus) => void;
  idleTimeout?: () => void;
}

export class StatusService implements IStatusService {
  private client: any;
  private config: StatusConfig;
  private botConfig: IBotConfig;
  private currentStatus: DiscordStatus = 'offline' as DiscordStatus;
  private lastActivity: Date = new Date();
  private idleTimer: NodeJS.Timeout | null = null;
  private eventHandlers: StatusManagerEvents = {};
  private ready: boolean = false;

  constructor(client: any, config: StatusConfig, botConfig: IBotConfig) {
    this.client = client;
    this.config = config;
    this.botConfig = botConfig;
  }

  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('📴 Status management is disabled');
      this.ready = true;
      return;
    }

    console.log('🔄 Initializing Status Service...');
    
    // Set initial status
    await this.setStatus(this.config.autoManagement.defaultStatus);
    
    // Start idle timer if auto-management is enabled
    if (this.config.autoManagement.goIdleAfterInactivity) {
      this.startIdleTimer();
    }

    this.ready = true;
    console.log(`✅ Status Service initialized with status: ${this.currentStatus}`);
  }

  async stop(): Promise<void> {
    console.log('🛑 Stopping Status Service...');
    
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
    }
    
    this.ready = false;
  }

  isReady(): boolean {
    return this.ready;
  }

  async setStatus(status: string): Promise<void> {
    if (!this.ready || !this.config.enabled) {
      return;
    }

    try {
      const oldStatus = this.currentStatus;
      const discordStatus = status as DiscordStatus;
      
      // Use the configured status mapping
      const statusToSet = this.config.statuses[status] || discordStatus;
      
      await this.client.change_status(statusToSet);
      this.currentStatus = discordStatus;
      
      console.log(`🔄 Status changed: ${oldStatus} → ${discordStatus}`);
      
      // Emit status changed event
      if (this.eventHandlers.statusChanged && oldStatus !== discordStatus) {
        this.eventHandlers.statusChanged(oldStatus, discordStatus);
      }
      
    } catch (error) {
      console.error('❌ Failed to change status:', error);
      throw error;
    }
  }

  getCurrentStatus(): string {
    return this.currentStatus;
  }

  async onMessageActivity(): Promise<void> {
    if (!this.ready || !this.config.enabled) {
      return;
    }

    this.lastActivity = new Date();

    // Set status to online if auto-management is enabled
    if (this.config.autoManagement.enabled && this.currentStatus !== 'online') {
      await this.setStatus('online');
    }

    // Reset idle timer
    if (this.config.autoManagement.goIdleAfterInactivity) {
      this.resetIdleTimer();
    }
  }

  getStats(): any {
    return {
      currentStatus: this.currentStatus,
      lastActivity: this.lastActivity,
      timeUntilIdle: this.getTimeUntilIdle(),
      config: this.config
    };
  }

  updateConfig(newConfig: Partial<StatusConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Status service configuration updated');
    
    // Restart idle timer if timeout changed
    if (newConfig.idleTimeout !== undefined && this.config.autoManagement.goIdleAfterInactivity) {
      this.resetIdleTimer();
    }
  }

  /**
   * Start the idle timer
   */
  private startIdleTimer(): void {
    if (!this.config.autoManagement.goIdleAfterInactivity) {
      return;
    }

    this.resetIdleTimer();
  }

  /**
   * Reset the idle timer
   */
  private resetIdleTimer(): void {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }

    if (!this.config.autoManagement.goIdleAfterInactivity) {
      return;
    }

    this.idleTimer = setTimeout(async () => {
      try {
        console.log('⏰ Idle timeout reached, setting status to idle');
        await this.setStatus('idle');
        
        if (this.eventHandlers.idleTimeout) {
          this.eventHandlers.idleTimeout();
        }
      } catch (error) {
        console.error('❌ Error setting idle status:', error);
      }
    }, this.config.idleTimeout * 60 * 1000); // Convert minutes to milliseconds
  }

  /**
   * Get time until idle (in minutes)
   */
  private getTimeUntilIdle(): number | null {
    if (!this.config.autoManagement.goIdleAfterInactivity || !this.idleTimer) {
      return null;
    }

    const timeSinceActivity = Date.now() - this.lastActivity.getTime();
    const timeUntilIdle = (this.config.idleTimeout * 60 * 1000) - timeSinceActivity;
    
    return Math.max(0, Math.ceil(timeUntilIdle / (60 * 1000))); // Return minutes
  }

  /**
   * Register event handlers
   */
  on<K extends keyof StatusManagerEvents>(event: K, handler: StatusManagerEvents[K]): void {
    this.eventHandlers[event] = handler;
  }

  /**
   * Remove event handlers
   */
  off<K extends keyof StatusManagerEvents>(event: K): void {
    delete this.eventHandlers[event];
  }

  /**
   * Force set status (bypasses auto-management temporarily)
   */
  async forceSetStatus(status: DiscordStatus): Promise<void> {
    console.log(`🔧 Force setting status to: ${status}`);
    await this.setStatus(status);
    
    // Reset idle timer to resume auto-management
    if (this.config.autoManagement.goIdleAfterInactivity) {
      this.resetIdleTimer();
    }
  }
}
