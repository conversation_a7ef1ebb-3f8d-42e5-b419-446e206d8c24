import { DiscordMessage, ConversationContext, ChatMessage } from '../types/message.js';
import { IBotConfig } from '../core/interfaces.js';
import * as fs from 'fs';
import * as path from 'path';

export class MessageContextManager {
  private contexts: Map<string, ConversationContext> = new Map();
  private dataDir: string;
  private config: IBotConfig;

  constructor(config: IBotConfig, dataDirectory: string = './data/conversations') {
    this.config = config;
    this.dataDir = dataDirectory;
    this.ensureDataDirectory();
    this.loadExistingContexts();
  }

  private ensureDataDirectory(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private loadExistingContexts(): void {
    try {
      const files = fs.readdirSync(this.dataDir);
      for (const file of files) {
        if (file.endsWith('.json')) {
          const channelId = file.replace('.json', '');
          const filePath = path.join(this.dataDir, file);
          const data = fs.readFileSync(filePath, 'utf8');
          const context: ConversationContext = JSON.parse(data);
          this.contexts.set(channelId, context);
        }
      }
      console.log(`Loaded ${this.contexts.size} conversation contexts`);
    } catch (error) {
      console.error('Error loading existing contexts:', error);
    }
  }

  async fetchAndStoreMessages(client: any, channelId: string, newMessage?: DiscordMessage): Promise<ConversationContext> {
    try {
      // Fetch recent messages from Discord
      const fetchedMessages = await client.fetch_messages(
        this.config.maxContextMessages,
        channelId,
        ""
      );

      // Add the new message if provided
      let allMessages = Array.isArray(fetchedMessages) ? [...fetchedMessages] : [];
      if (newMessage) {
        // Check if the new message is already in the fetched messages
        const messageExists = allMessages.some(msg => msg.id === newMessage.id);
        if (!messageExists) {
          allMessages.unshift(newMessage);
        }
      }

      // Sort messages by timestamp (oldest first)
      allMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

      // Keep only the last N messages
      const limitedMessages = allMessages.slice(-this.config.maxStoredMessages);

      const context: ConversationContext = {
        channelId,
        messages: limitedMessages,
        lastUpdated: new Date().toISOString(),
        messageCount: limitedMessages.length
      };

      // Store in memory and save to file
      this.contexts.set(channelId, context);
      await this.saveContextToFile(context);

      console.log(`Updated context for channel ${channelId}: ${limitedMessages.length} messages`);
      return context;

    } catch (error) {
      console.error(`Error fetching messages for channel ${channelId}:`, error);
      
      // Return existing context or create empty one
      const existingContext = this.contexts.get(channelId);
      if (existingContext) {
        return existingContext;
      }

      const emptyContext: ConversationContext = {
        channelId,
        messages: newMessage ? [newMessage] : [],
        lastUpdated: new Date().toISOString(),
        messageCount: newMessage ? 1 : 0
      };

      this.contexts.set(channelId, emptyContext);
      await this.saveContextToFile(emptyContext);
      return emptyContext;
    }
  }

  private async saveContextToFile(context: ConversationContext): Promise<void> {
    try {
      const filePath = path.join(this.dataDir, `${context.channelId}.json`);
      const data = JSON.stringify(context, null, 2);
      fs.writeFileSync(filePath, data, 'utf8');
    } catch (error) {
      console.error(`Error saving context for channel ${context.channelId}:`, error);
    }
  }

  getContext(channelId: string): DiscordMessage[] {
    const context = this.contexts.get(channelId);
    return context ? context.messages : [];
  }

  convertToChatMessages(messages: DiscordMessage[], botUserId?: string): ChatMessage[] {
    const chatMessages: ChatMessage[] = [];

    for (const message of messages) {
      // Skip empty messages
      if (!message.content || message.content.trim() === '') {
        continue;
      }

      // Determine role based on author
      let role: 'user' | 'assistant';
      if (botUserId && message.author.id === botUserId) {
        role = 'assistant';
      } else {
        role = 'user';
      }

      chatMessages.push({
        role,
        content: message.content,
        timestamp: message.timestamp,
        author: message.author.username || message.author.global_name || message.author.id
      });
    }

    return chatMessages;
  }

  addMessageToContext(channelId: string, message: DiscordMessage): void {
    let context = this.contexts.get(channelId);
    
    if (!context) {
      context = {
        channelId,
        messages: [],
        lastUpdated: new Date().toISOString(),
        messageCount: 0
      };
    }

    // Add message if it doesn't already exist
    const messageExists = context.messages.some(msg => msg.id === message.id);
    if (!messageExists) {
      context.messages.push(message);
      
      // Keep only the last N messages
      if (context.messages.length > this.config.maxStoredMessages) {
        context.messages = context.messages.slice(-this.config.maxStoredMessages);
      }
      
      context.messageCount = context.messages.length;
      context.lastUpdated = new Date().toISOString();
      
      this.contexts.set(channelId, context);
      this.saveContextToFile(context);
    }
  }

  clearContext(channelId: string): void {
    this.contexts.delete(channelId);
    try {
      const filePath = path.join(this.dataDir, `${channelId}.json`);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error(`Error clearing context file for channel ${channelId}:`, error);
    }
  }

  getAllChannels(): string[] {
    return Array.from(this.contexts.keys());
  }
}
